import React, { useRef, useState } from "react";
import { formatTickersUpdate } from "@/utils/format";
import { TTickerUpdate } from "@/components/PairMarket/services/TickerHandler";
import {
  getArrTickerRoom,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { useCallback, useEffect } from "react";
import { TradingPair, TPairSetting, ETradingStatus } from "@/types/pair";
import rf from "@/services/RequestFactory";
import { formatPrice } from "@/utils/format";
import { getPriceStyle } from "@/utils/helper";
import AppNumber from "./AppNumber";

const PairMarketLanding = () => {
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );
  const pairTickersRef = useRef<Record<string, TradingPair>>({});
  const frameIdRef = useRef<number | null>(null);
  const [renderCount, setRenderCount] = useState<number>(0);
  const [validPairs, setValidPairs] = useState<TPairSetting[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  const handleArrTickersUpdate = useCallback((data: TBroadcastEvent) => {
    const tickersUpdated = formatTickersUpdate(
      JSON.parse(data.detail) as TTickerUpdate[]
    );

    tickersUpdated.forEach((ticker) => {
      const symbol = ticker.symbol;

      // Update or create ticker data for this symbol in the ref
      if (!pairTickersRef.current[symbol]) {
        pairTickersRef.current[symbol] = {} as TradingPair;
      }

      pairTickersRef.current[symbol] = ticker;
    });

    if (frameIdRef.current !== null) {
      cancelAnimationFrame(frameIdRef.current);
    }

    frameIdRef.current = requestAnimationFrame(() => {
      setRenderCount((prev) => prev + 1);
      frameIdRef.current = null;
    });

    return () => {
      if (frameIdRef.current !== null) {
        cancelAnimationFrame(frameIdRef.current);
      }
    };
  }, []);

  // Fetch trading pairs and filter valid ones
  useEffect(() => {
    const initTradingPairs = async () => {
      console.log("wtf ");
      try {
        setLoading(true);
        const data = await rf.getRequest("PairRequest").getTradingPairs();
        const pairs = data?.data || [];

        // Filter only ACTIVE pairs
        const activePairs = pairs.filter(
          (pair: TPairSetting) => pair.status === ETradingStatus.ACTIVE
        );

        const defaultPairs = [
          {
            symbol: "ETHUSDT",
            name: "Ethereum",
          },
          {
            symbol: "BTCUSDT",
            name: "Bitcoin",
          },
        ];
        const finalPairs = defaultPairs
          .map((symbol) =>
            activePairs.find(
              (pair: TPairSetting) =>
                pair.symbol?.toUpperCase() === symbol.symbol
            )
          )
          .filter(Boolean) as TPairSetting[];

        setValidPairs(finalPairs);
      } catch (error) {
        console.log("init trading pairs error", error);
      } finally {
        setLoading(false);
      }
    };

    initTradingPairs();
  }, []);

  // Subscribe to tickers updates
  useEffect(() => {
    if (!socketConnected) return;
    subscribeSocketChannel({
      params: [getArrTickerRoom()],
    });

    AppBroadcast.on(
      BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
      handleArrTickersUpdate
    );

    return () => {
      // Clean up socket and event listeners
      unsubscribeSocketChannel({
        params: [getArrTickerRoom()],
      });

      AppBroadcast.remove(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        handleArrTickersUpdate
      );
    };
  }, [handleArrTickersUpdate, socketConnected]);

  if (loading) {
    return (
      <div className="flex flex-col justify-between gap-8">
        <div className="text-white-500 text-center">Loading pairs...</div>
      </div>
    );
  }

  if (validPairs.length === 0) {
    return (
      <div className="flex flex-col justify-between gap-8">
        <div className="text-white-500 text-center">
          No valid pairs available
        </div>
      </div>
    );
  }

  return (
    <div className="">
      {validPairs.map((pair) => {
        const ticker = pairTickersRef.current[pair.symbol];
        const lastPrice = ticker?.lastPrice || "0";
        const priceChangePercent = ticker?.priceChangePercent || "0";
        const priceChange = ticker?.priceChange || "0";
        const isPositive = parseFloat(priceChangePercent) >= 0;

        return (
          <div key={pair.symbol} className="flex justify-between gap-16">
            <div>
              <div className="heading-lg-semibold-32 mb-2">
                {pair.baseAsset}
              </div>
              <div className="text-white-500 text-[20px] font-medium leading-[1.2]">
                {pair.name}
              </div>
            </div>
            <div>
              <div className="heading-lg-semibold-32 mb-2 text-right">
                <AppNumber
                  value={lastPrice}
                  decimals={pair.pricePrecision}
                  isForUSD
                  isFormatLargeNumber={false}
                />
              </div>
              <div
                className="text-right text-[20px] font-medium leading-[1.2]"
                style={{ color: getPriceStyle(priceChange) }}
              >
                {isPositive ? "+" : ""}
                {parseFloat(priceChangePercent).toFixed(2)}%
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PairMarketLanding;
