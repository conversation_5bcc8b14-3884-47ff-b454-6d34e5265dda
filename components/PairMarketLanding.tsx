import React, { useRef, useState } from "react";
import { formatTickersUpdate } from "@/utils/format";
import { TTickerUpdate } from "@/components/PairMarket/services/TickerHandler";
import { setTradingPairs } from "@/store/tradingPair.store";
import {
  getArrTickerRoom,
  subscribeSocketChannel,
  unsubscribeSocketChannel,
} from "@/libs/socket";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { useCallback, useEffect } from "react";
import { TradingPair } from "@/types/pair";

const PairMarketLanding = () => {
  const socketConnected = useSelector(
    (state: RootState) => state.metadata.socketConnected
  );
  const pairTickersRef = useRef<Record<string, TradingPair>>({});
  const frameIdRef = useRef<number | null>(null);
  const [renderCount, setRenderCount] = useState<number>(0);

  const handleArrTickersUpdate = useCallback((data: TBroadcastEvent) => {
    const tickersUpdated = formatTickersUpdate(
      JSON.parse(data.detail) as TTickerUpdate[]
    );

    tickersUpdated.forEach((ticker) => {
      const symbol = ticker.symbol;

      // Update or create ticker data for this symbol in the ref
      if (!pairTickersRef.current[symbol]) {
        pairTickersRef.current[symbol] = {} as TradingPair;
      }

      pairTickersRef.current[symbol] = ticker;
    });

    if (frameIdRef.current !== null) {
      cancelAnimationFrame(frameIdRef.current);
    }

    frameIdRef.current = requestAnimationFrame(() => {
      setRenderCount((prev) => prev + 1);
      frameIdRef.current = null;
    });

    return () => {
      if (frameIdRef.current !== null) {
        cancelAnimationFrame(frameIdRef.current);
      }
    };
  }, []);

  // Subscribe to tickers updates
  useEffect(() => {
    if (!socketConnected) return;
    subscribeSocketChannel({
      params: [getArrTickerRoom()],
    });

    AppBroadcast.on(
      BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
      handleArrTickersUpdate
    );

    return () => {
      // Clean up socket and event listeners
      unsubscribeSocketChannel({
        params: [getArrTickerRoom()],
      });

      AppBroadcast.remove(
        BROADCAST_EVENTS.ARR_TICKERS_UPDATED,
        handleArrTickersUpdate
      );
    };
  }, [handleArrTickersUpdate, socketConnected]);

  return (
    <div className="flex flex-col justify-between gap-8">
      <div className="flex justify-between gap-16">
        <div>
          <div className="heading-lg-semibold-32 mb-2">BTC</div>
          <div className="text-white-500 text-[20px] font-medium leading-[1.2]">
            Bitcoin
          </div>
        </div>
        <div>
          <div className="heading-lg-semibold-32 mb-2">105,424.23</div>
          <div className="text-right text-[20px] font-medium leading-[1.2] text-green-500">
            + 15.32%
          </div>
        </div>
      </div>
      <div className="flex justify-between gap-16">
        <div>
          <div className="heading-lg-semibold-32 mb-2">ETH</div>
          <div className="text-white-500 text-[20px] font-medium leading-[1.2]">
            Etherium
          </div>
        </div>
        <div>
          <div className="heading-lg-semibold-32 mb-2 text-right">2,555.23</div>
          <div className="text-right text-[20px] font-medium leading-[1.2] text-green-500">
            + 15.32%
          </div>
        </div>
      </div>
    </div>
  );
};

export default PairMarketLanding;
