import { createSlice, PayloadAction } from "@reduxjs/toolkit";

interface FavoritesState {
  favoritePairs: string[];
}

const initialState: FavoritesState = {
  favoritePairs: [],
};

export const favoritesSlice = createSlice({
  name: "favorites",
  initialState,
  reducers: {
    setFavoritePairs: (state, action: PayloadAction<string[]>) => {
      state.favoritePairs = action.payload;
    },
    toggleFavoritePair: (state, action: PayloadAction<string>) => {
      const symbol = action.payload.toUpperCase();
      const index = state.favoritePairs.indexOf(symbol);

      if (index > -1) {
        state.favoritePairs.splice(index, 1);
      } else {
        state.favoritePairs.push(symbol);
      }

      // Persist to localStorage
      try {
        localStorage.setItem(
          "favoritePairs",
          JSON.stringify(state.favoritePairs)
        );
      } catch (error) {
        console.log("Error saving favorites:", error);
      }
    },
    loadFavoritesFromStorage: (state) => {
      try {
        const storedFavorites = localStorage.getItem("favoritePairs");
        if (storedFavorites) {
          state.favoritePairs = JSON.parse(storedFavorites);
        }
      } catch (error) {
        console.log("Error loading favorites:", error);
      }
    },
  },
});

export const {
  setFavoritePairs,
  toggleFavoritePair,
  loadFavoritesFromStorage,
} = favoritesSlice.actions;

export default favoritesSlice.reducer;
