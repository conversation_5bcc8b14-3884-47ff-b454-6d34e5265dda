import { combineReducers, configureStore } from "@reduxjs/toolkit";
import user from "./user.store";
import tradingPair from "./tradingPair.store";
import account from "./account.store";
import metadata from "./metadata.store";
import favorites from "./favorites.store";

const rootReducer = combineReducers({
  user,
  tradingPair,
  account,
  metadata,
  favorites,
});

const store = configureStore({
  reducer: rootReducer,
});

export type RootState = ReturnType<typeof store.getState>;

export { store };
